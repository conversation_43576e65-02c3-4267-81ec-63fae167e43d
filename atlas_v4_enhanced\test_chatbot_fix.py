#!/usr/bin/env python3
"""
Test script to verify the chatbot AIResponse fix
"""

import asyncio
import json
import httpx
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_chatbot_messages():
    """Test various chatbot messages to verify the fix"""
    
    base_url = "http://localhost:8002"
    
    test_messages = [
        {
            "name": "Simple Greeting",
            "message": "Hello",
            "expected_intent": "greeting"
        },
        {
            "name": "Stock Query",
            "message": "What is AAPL trading at?",
            "expected_intent": "stock_analysis"
        },
        {
            "name": "General Question",
            "message": "How does the Lee Method work?",
            "expected_intent": "education"
        }
    ]
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        for test in test_messages:
            try:
                logger.info(f"\n🧪 Testing: {test['name']}")
                logger.info(f"📝 Message: '{test['message']}'")
                
                # Send chat message
                response = await client.post(
                    f"{base_url}/api/v1/chat/message",
                    json={
                        "message": test["message"],
                        "context": "general",
                        "session_id": f"test_{test['name'].lower().replace(' ', '_')}"
                    }
                )
                
                if response.status_code == 200:
                    data = response.json()
                    logger.info(f"✅ SUCCESS: {test['name']}")
                    logger.info(f"📤 Response: {data.get('response', '')[:100]}...")
                    logger.info(f"🎯 Intent: {data.get('intent_type', 'unknown')}")
                    logger.info(f"🧠 Grok Powered: {data.get('grok_powered', False)}")
                    logger.info(f"📊 Confidence: {data.get('confidence', 0):.2f}")
                    
                    # Check if we got the expected response without errors
                    if "AIResponse.__init__()" in data.get('response', ''):
                        logger.error(f"❌ STILL HAS ERROR: {test['name']}")
                    else:
                        logger.info(f"✅ NO ERRORS: {test['name']}")
                        
                else:
                    logger.error(f"❌ HTTP ERROR: {test['name']} - Status: {response.status_code}")
                    logger.error(f"Response: {response.text}")
                    
            except Exception as e:
                logger.error(f"❌ EXCEPTION: {test['name']} - {e}")
                
            # Wait between tests
            await asyncio.sleep(1)

if __name__ == "__main__":
    asyncio.run(test_chatbot_messages())
