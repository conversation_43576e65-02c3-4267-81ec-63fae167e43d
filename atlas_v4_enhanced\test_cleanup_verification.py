#!/usr/bin/env python3
"""
Test script to verify that the code cleanup resolved import conflicts
and that core functionality is working properly.
"""

import asyncio
import sys
import os
import traceback

# Add current directory to path
sys.path.append(os.path.dirname(__file__))

async def test_lee_method_scanner():
    """Test that LeeMethodScanner imports and has required methods"""
    try:
        print("🔍 Testing LeeMethodScanner import and methods...")
        
        # Test import
        from atlas_lee_method import LeeMethodScanner, AtlasLeeMethodRealtimeScanner
        print("✅ Successfully imported LeeMethodScanner classes")
        
        # Test instantiation
        scanner = LeeMethodScanner()
        print("✅ Successfully instantiated LeeMethodScanner")
        
        # Test that scan_multiple_symbols method exists
        if hasattr(scanner, 'scan_multiple_symbols'):
            print("✅ scan_multiple_symbols method exists")
        else:
            print("❌ scan_multiple_symbols method missing")
            return False
            
        # Test that scan_symbol method exists
        if hasattr(scanner, 'scan_symbol'):
            print("✅ scan_symbol method exists")
        else:
            print("❌ scan_symbol method missing")
            return False
            
        # Test realtime scanner
        realtime_scanner = AtlasLeeMethodRealtimeScanner()
        print("✅ Successfully instantiated AtlasLeeMethodRealtimeScanner")
        
        if hasattr(realtime_scanner, 'scan_multiple_symbols'):
            print("✅ AtlasLeeMethodRealtimeScanner has scan_multiple_symbols method")
        else:
            print("❌ AtlasLeeMethodRealtimeScanner missing scan_multiple_symbols method")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ LeeMethodScanner test failed: {e}")
        traceback.print_exc()
        return False

async def test_grok_integration():
    """Test that Grok integration imports properly"""
    try:
        print("\n🔍 Testing Grok integration import...")
        
        # Test import
        from atlas_grok_integration import (
            GrokRequest, GrokResponse, GrokTaskType, GrokCapability,
            AtlasGrokIntegrationEngine
        )
        print("✅ Successfully imported Grok integration classes")
        
        # Test that we have proper dataclass implementations
        if hasattr(GrokRequest, '__dataclass_fields__'):
            print("✅ GrokRequest is proper dataclass")
        else:
            print("❌ GrokRequest is not a proper dataclass")
            return False
            
        if hasattr(GrokResponse, '__dataclass_fields__'):
            print("✅ GrokResponse is proper dataclass")
        else:
            print("❌ GrokResponse is not a proper dataclass")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Grok integration test failed: {e}")
        traceback.print_exc()
        return False

async def test_core_imports():
    """Test core system imports"""
    try:
        print("\n🔍 Testing core system imports...")
        
        # Test config and models
        from config import settings
        from models import EngineStatus
        print("✅ Successfully imported config and models")
        
        # Test market core
        from atlas_market_core import AtlasMarketEngine
        print("✅ Successfully imported AtlasMarketEngine")
        
        # Test orchestrator
        from atlas_multi_agent_orchestrator import AtlasMultiAgentOrchestrator
        print("✅ Successfully imported AtlasMultiAgentOrchestrator")
        
        return True
        
    except Exception as e:
        print(f"❌ Core imports test failed: {e}")
        traceback.print_exc()
        return False

async def main():
    """Run all verification tests"""
    print("🚀 Starting Atlas V5 Enhanced cleanup verification tests...")
    print("=" * 60)
    
    tests = [
        ("LeeMethodScanner", test_lee_method_scanner),
        ("Grok Integration", test_grok_integration),
        ("Core Imports", test_core_imports)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    print("\n" + "=" * 60)
    print("📊 VERIFICATION RESULTS:")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("=" * 60)
    if all_passed:
        print("🎉 ALL TESTS PASSED - Cleanup was successful!")
        print("✅ Core functionality is working properly")
        print("✅ No import conflicts detected")
        print("✅ scan_multiple_symbols method is available")
    else:
        print("⚠️  SOME TESTS FAILED - Additional cleanup may be needed")
    
    return all_passed

if __name__ == "__main__":
    asyncio.run(main())
