#!/usr/bin/env python3
"""
Atlas Enhanced API Manager
Provides improved API connectivity, error handling, and connection management
"""

import asyncio
import logging
import time
import json
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import aiohttp
import httpx
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)

class APIStatus(Enum):
    """API connection status"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    FAILED = "failed"
    UNKNOWN = "unknown"

@dataclass
class APIEndpoint:
    """Configuration for an API endpoint"""
    name: str
    base_url: str
    api_key: Optional[str] = None
    timeout: int = 30
    max_retries: int = 3
    retry_delay: float = 1.0
    rate_limit_per_minute: int = 60
    health_check_path: Optional[str] = None
    required_headers: Dict[str, str] = field(default_factory=dict)
    
@dataclass
class APIMetrics:
    """Metrics for API performance tracking"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    average_response_time: float = 0.0
    last_request_time: Optional[datetime] = None
    last_error: Optional[str] = None
    status: APIStatus = APIStatus.UNKNOWN

class EnhancedAPIManager:
    """Enhanced API manager with improved connectivity and error handling"""
    
    def __init__(self):
        self.endpoints: Dict[str, APIEndpoint] = {}
        self.metrics: Dict[str, APIMetrics] = {}
        self.rate_limiters: Dict[str, List[float]] = {}
        self.circuit_breakers: Dict[str, Dict] = {}
        self.session: Optional[aiohttp.ClientSession] = None
        self.health_check_interval = 300  # 5 minutes
        self.health_check_task: Optional[asyncio.Task] = None
        
    async def initialize(self):
        """Initialize the API manager"""
        try:
            # Create aiohttp session with optimized settings
            connector = aiohttp.TCPConnector(
                limit=100,  # Total connection pool size
                limit_per_host=30,  # Per-host connection limit
                ttl_dns_cache=300,  # DNS cache TTL
                use_dns_cache=True,
                keepalive_timeout=30,
                enable_cleanup_closed=True
            )
            
            timeout = aiohttp.ClientTimeout(total=60, connect=10)
            
            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers={
                    'User-Agent': 'Atlas-V5-Enhanced/1.0',
                    'Accept': 'application/json',
                    'Connection': 'keep-alive'
                }
            )
            
            # Initialize default endpoints
            await self._setup_default_endpoints()
            
            # Start health check monitoring
            self.health_check_task = asyncio.create_task(self._health_check_loop())
            
            logger.info("✅ Enhanced API Manager initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Enhanced API Manager: {e}")
            return False
    
    async def _setup_default_endpoints(self):
        """Setup default API endpoints for Atlas integrations with enhanced configuration"""
        # Load API keys from config
        try:
            from config import get_api_config
            fmp_config = get_api_config('fmp')
            alpaca_config = get_api_config('alpaca')
            grok_config = get_api_config('grok')
        except:
            fmp_config = alpaca_config = grok_config = {}

        # FMP (Financial Modeling Prep) - Enhanced with failover
        fmp_headers = {}
        if fmp_config.get('api_key'):
            fmp_headers['apikey'] = fmp_config['api_key']

        await self.register_endpoint(APIEndpoint(
            name="fmp",
            base_url="https://financialmodelingprep.com/api/v3",
            api_key=fmp_config.get('api_key'),
            timeout=15,
            max_retries=3,
            rate_limit_per_minute=300,
            health_check_path="/profile/AAPL",
            required_headers=fmp_headers
        ))

        # FMP Backup endpoint (different subdomain for failover)
        await self.register_endpoint(APIEndpoint(
            name="fmp_backup",
            base_url="https://fmpcloud.io/api/v3",
            api_key=fmp_config.get('api_key'),
            timeout=20,
            max_retries=2,
            rate_limit_per_minute=200,
            health_check_path="/profile/AAPL",
            required_headers=fmp_headers
        ))

        # Alpaca Trading API - Enhanced with proper authentication
        alpaca_headers = {}
        if alpaca_config.get('api_key') and alpaca_config.get('secret_key'):
            alpaca_headers.update({
                'APCA-API-KEY-ID': alpaca_config['api_key'],
                'APCA-API-SECRET-KEY': alpaca_config['secret_key']
            })

        await self.register_endpoint(APIEndpoint(
            name="alpaca",
            base_url="https://paper-api.alpaca.markets/v2",
            timeout=20,
            max_retries=2,
            rate_limit_per_minute=200,
            health_check_path="/account",
            required_headers=alpaca_headers
        ))

        # Alpaca Market Data API (separate endpoint for market data)
        await self.register_endpoint(APIEndpoint(
            name="alpaca_data",
            base_url="https://data.alpaca.markets/v2",
            timeout=15,
            max_retries=3,
            rate_limit_per_minute=200,
            health_check_path="/stocks/bars/latest",
            required_headers=alpaca_headers
        ))

        # Grok AI API - Enhanced with proper authentication
        grok_headers = {'Content-Type': 'application/json'}
        if grok_config.get('api_key'):
            grok_headers['Authorization'] = f"Bearer {grok_config['api_key']}"

        await self.register_endpoint(APIEndpoint(
            name="grok",
            base_url="https://api.x.ai/v1",
            api_key=grok_config.get('api_key'),
            timeout=45,
            max_retries=2,
            rate_limit_per_minute=60,
            health_check_path="/models",
            required_headers=grok_headers
        ))

        # Internal Atlas APIs
        await self.register_endpoint(APIEndpoint(
            name="atlas_internal",
            base_url="http://localhost:8002/api/v1",
            timeout=10,
            max_retries=1,
            rate_limit_per_minute=1000,
            health_check_path="/health"
        ))

        # Yahoo Finance (free backup for market data)
        await self.register_endpoint(APIEndpoint(
            name="yahoo_finance",
            base_url="https://query1.finance.yahoo.com/v8",
            timeout=10,
            max_retries=2,
            rate_limit_per_minute=100,
            health_check_path="/finance/chart/AAPL"
        ))
    
    async def register_endpoint(self, endpoint: APIEndpoint):
        """Register a new API endpoint"""
        self.endpoints[endpoint.name] = endpoint
        self.metrics[endpoint.name] = APIMetrics()
        self.rate_limiters[endpoint.name] = []
        self.circuit_breakers[endpoint.name] = {
            'failures': 0,
            'last_failure': None,
            'state': 'closed',  # closed, open, half-open
            'failure_threshold': 5,
            'recovery_timeout': 60
        }
        
        logger.info(f"📡 Registered API endpoint: {endpoint.name}")
    
    async def make_request(self, 
                          endpoint_name: str, 
                          method: str, 
                          path: str, 
                          **kwargs) -> Dict[str, Any]:
        """Make an API request with enhanced error handling and retry logic"""
        
        if endpoint_name not in self.endpoints:
            raise ValueError(f"Unknown endpoint: {endpoint_name}")
        
        endpoint = self.endpoints[endpoint_name]
        metrics = self.metrics[endpoint_name]
        
        # Check circuit breaker
        if not await self._check_circuit_breaker(endpoint_name):
            raise Exception(f"Circuit breaker open for {endpoint_name}")
        
        # Check rate limiting
        if not await self._check_rate_limit(endpoint_name):
            raise Exception(f"Rate limit exceeded for {endpoint_name}")
        
        # Prepare request
        url = f"{endpoint.base_url.rstrip('/')}/{path.lstrip('/')}"
        headers = {**endpoint.required_headers, **kwargs.get('headers', {})}
        
        # Add API key if available
        if endpoint.api_key:
            if 'Authorization' not in headers:
                headers['Authorization'] = f"Bearer {endpoint.api_key}"
        
        start_time = time.time()
        last_exception = None
        
        # Retry logic
        for attempt in range(endpoint.max_retries + 1):
            try:
                metrics.total_requests += 1
                
                async with self.session.request(
                    method=method.upper(),
                    url=url,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=endpoint.timeout),
                    **{k: v for k, v in kwargs.items() if k != 'headers'}
                ) as response:
                    
                    response_time = time.time() - start_time
                    
                    if response.status == 200:
                        # Success
                        metrics.successful_requests += 1
                        metrics.last_request_time = datetime.now()
                        
                        # Update average response time
                        if metrics.average_response_time == 0:
                            metrics.average_response_time = response_time
                        else:
                            metrics.average_response_time = (
                                metrics.average_response_time * 0.8 + response_time * 0.2
                            )
                        
                        # Reset circuit breaker on success
                        await self._reset_circuit_breaker(endpoint_name)
                        
                        # Parse response
                        try:
                            data = await response.json()
                            return {
                                'success': True,
                                'data': data,
                                'status_code': response.status,
                                'response_time': response_time,
                                'endpoint': endpoint_name
                            }
                        except json.JSONDecodeError:
                            text = await response.text()
                            return {
                                'success': True,
                                'data': text,
                                'status_code': response.status,
                                'response_time': response_time,
                                'endpoint': endpoint_name
                            }
                    
                    else:
                        # HTTP error
                        error_text = await response.text()
                        raise aiohttp.ClientResponseError(
                            request_info=response.request_info,
                            history=response.history,
                            status=response.status,
                            message=error_text
                        )
            
            except Exception as e:
                last_exception = e
                metrics.failed_requests += 1
                metrics.last_error = str(e)
                
                # Record circuit breaker failure
                await self._record_failure(endpoint_name)
                
                # Log attempt
                logger.warning(f"API request failed (attempt {attempt + 1}/{endpoint.max_retries + 1}) "
                             f"for {endpoint_name}: {e}")
                
                # Wait before retry (exponential backoff)
                if attempt < endpoint.max_retries:
                    wait_time = endpoint.retry_delay * (2 ** attempt)
                    await asyncio.sleep(wait_time)
        
        # All retries failed
        return {
            'success': False,
            'error': str(last_exception),
            'endpoint': endpoint_name,
            'attempts': endpoint.max_retries + 1
        }
    
    async def _check_rate_limit(self, endpoint_name: str) -> bool:
        """Check if request is within rate limits"""
        endpoint = self.endpoints[endpoint_name]
        now = time.time()
        
        # Clean old timestamps
        cutoff = now - 60  # 1 minute ago
        self.rate_limiters[endpoint_name] = [
            t for t in self.rate_limiters[endpoint_name] if t > cutoff
        ]
        
        # Check if under limit
        if len(self.rate_limiters[endpoint_name]) < endpoint.rate_limit_per_minute:
            self.rate_limiters[endpoint_name].append(now)
            return True
        
        return False
    
    async def _check_circuit_breaker(self, endpoint_name: str) -> bool:
        """Check circuit breaker state"""
        breaker = self.circuit_breakers[endpoint_name]
        now = time.time()
        
        if breaker['state'] == 'closed':
            return True
        elif breaker['state'] == 'open':
            # Check if recovery timeout has passed
            if (breaker['last_failure'] and 
                now - breaker['last_failure'] > breaker['recovery_timeout']):
                breaker['state'] = 'half-open'
                logger.info(f"Circuit breaker half-open for {endpoint_name}")
                return True
            return False
        elif breaker['state'] == 'half-open':
            return True
        
        return False
    
    async def _record_failure(self, endpoint_name: str):
        """Record a failure for circuit breaker"""
        breaker = self.circuit_breakers[endpoint_name]
        breaker['failures'] += 1
        breaker['last_failure'] = time.time()
        
        if breaker['failures'] >= breaker['failure_threshold']:
            breaker['state'] = 'open'
            logger.warning(f"Circuit breaker opened for {endpoint_name}")
    
    async def _reset_circuit_breaker(self, endpoint_name: str):
        """Reset circuit breaker on successful request"""
        breaker = self.circuit_breakers[endpoint_name]
        if breaker['state'] in ['half-open', 'open']:
            breaker['state'] = 'closed'
            breaker['failures'] = 0
            logger.info(f"Circuit breaker reset for {endpoint_name}")
    
    async def _health_check_loop(self):
        """Continuous health check monitoring"""
        while True:
            try:
                await asyncio.sleep(self.health_check_interval)
                await self.perform_health_checks()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Health check loop error: {e}")
    
    async def perform_health_checks(self):
        """Perform health checks on all endpoints"""
        for endpoint_name, endpoint in self.endpoints.items():
            if endpoint.health_check_path:
                try:
                    result = await self.make_request(
                        endpoint_name, 'GET', endpoint.health_check_path
                    )
                    
                    if result.get('success'):
                        self.metrics[endpoint_name].status = APIStatus.HEALTHY
                    else:
                        self.metrics[endpoint_name].status = APIStatus.FAILED
                        
                except Exception as e:
                    self.metrics[endpoint_name].status = APIStatus.FAILED
                    logger.warning(f"Health check failed for {endpoint_name}: {e}")
    
    async def make_request_with_failover(self,
                                        primary_endpoint: str,
                                        fallback_endpoints: List[str],
                                        method: str,
                                        path: str,
                                        **kwargs) -> Dict[str, Any]:
        """Make API request with automatic failover to backup endpoints"""

        all_endpoints = [primary_endpoint] + fallback_endpoints
        last_error = None

        for endpoint_name in all_endpoints:
            try:
                result = await self.make_request(endpoint_name, method, path, **kwargs)

                if result.get('success'):
                    # Log successful failover if not using primary
                    if endpoint_name != primary_endpoint:
                        logger.info(f"✅ Successful failover from {primary_endpoint} to {endpoint_name}")
                    return result
                else:
                    last_error = result.get('error', 'Unknown error')
                    logger.warning(f"⚠️ {endpoint_name} failed: {last_error}")

            except Exception as e:
                last_error = str(e)
                logger.warning(f"⚠️ {endpoint_name} exception: {last_error}")
                continue

        # All endpoints failed
        return {
            'success': False,
            'error': f"All endpoints failed. Last error: {last_error}",
            'attempted_endpoints': all_endpoints
        }

    async def get_market_data_with_failover(self, symbol: str) -> Dict[str, Any]:
        """Get market data with automatic failover between data sources"""

        # Define failover chain: FMP -> FMP Backup -> Yahoo Finance
        return await self.make_request_with_failover(
            primary_endpoint="fmp",
            fallback_endpoints=["fmp_backup", "yahoo_finance"],
            method="GET",
            path=f"/quote/{symbol}"
        )

    async def get_trading_account_with_failover(self) -> Dict[str, Any]:
        """Get trading account info with failover"""

        return await self.make_request_with_failover(
            primary_endpoint="alpaca",
            fallback_endpoints=[],  # No fallback for account data
            method="GET",
            path="/account"
        )

    async def make_grok_request_with_failover(self, prompt: str) -> Dict[str, Any]:
        """Make Grok AI request with enhanced error handling"""

        request_data = {
            "model": "grok-beta",
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0.1,
            "max_tokens": 2000
        }

        return await self.make_request_with_failover(
            primary_endpoint="grok",
            fallback_endpoints=[],  # No fallback for Grok
            method="POST",
            path="/chat/completions",
            json=request_data
        )

    def get_metrics(self) -> Dict[str, Dict[str, Any]]:
        """Get performance metrics for all endpoints"""
        result = {}
        for name, metrics in self.metrics.items():
            success_rate = 0
            if metrics.total_requests > 0:
                success_rate = metrics.successful_requests / metrics.total_requests * 100

            result[name] = {
                'status': metrics.status.value,
                'total_requests': metrics.total_requests,
                'success_rate': round(success_rate, 2),
                'average_response_time': round(metrics.average_response_time, 3),
                'last_request': metrics.last_request_time.isoformat() if metrics.last_request_time else None,
                'last_error': metrics.last_error,
                'circuit_breaker_state': self.circuit_breakers[name]['state']
            }

        return result
    
    async def close(self):
        """Clean up resources"""
        if self.health_check_task:
            self.health_check_task.cancel()
            try:
                await self.health_check_task
            except asyncio.CancelledError:
                pass
        
        if self.session:
            await self.session.close()
        
        logger.info("Enhanced API Manager closed")

# Global instance
enhanced_api_manager = EnhancedAPIManager()
