#!/usr/bin/env python3
"""
A.T.L.A.S. V4 Enhanced - Manual Test Runner
Tests the specific questions provided by the user through the web interface
"""

import requests
import json
import time
from datetime import datetime
from typing import Dict, List, Any

class AtlasManualTestRunner:
    """Manual test runner for specific user questions"""
    
    def __init__(self, base_url: str = "http://localhost:8002"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.timeout = 30
        
        # Test questions organized by category
        self.test_questions = {
            "Category A - Core AI & Conversational Features": [
                "What's the difference between a market order and a limit order?",
                "Explain put‑call parity in simple terms.",
                "Give me a 6‑Point analysis for TSLA today.",
                "What's your market view on AAPL?",  # For Grok test
                "Tell me about AAPL trend",  # Context test part 1
                "What if I wanted a tighter stop?",  # Context test part 2
                "Give me a beginner‑level explanation of RSI",
                "Now explain it like I'm an expert",
                "I'm freaked out by volatility. How do I manage risk?"
            ],
            "Category B - Trading & Analysis Features": [
                "Scan MSFT on daily for the Lee Method signal—what do you see?",
                "What does the current TTM Squeeze histogram show for NVDA at 15 min?",
                "List the top 5 S&P 500 stocks with Lee Method buy signals right now.",
                "Provide a 6‑Point trade plan for GOOGL at market open.",
                "Construct an Iron Condor on SPY expiring in 30 days targeting $500 profit, moderate risk.",
                "Optimize a portfolio of AAPL, TSLA, NVDA for 10% annualized return.",
                "Calculate my portfolio's 95% VaR.",
                "Which market regime are we in—bull, bear, or sideways?"
            ],
            "Category C - Market Data & Intelligence": [
                "What's AMZN trading at right now?",
                "What's TSLA's last price?",  # For fallback test
                "Summarize the latest headlines impacting AAPL.",
                "What's the Twitter sentiment on GME today?",
                "Forecast MSFT's next 5 days using LSTM.",
                "Use satellite oil‐rig data to gauge energy sector outlook.",
                "Get live quotes for 7203.T (Toyota) on TSE."
            ],
            "Category D - Real-time Capabilities": [
                "Subscribe to TSLA scanner alerts—did you get a sub‑second notification?",
                "Scan 500 symbols and verify results return within 5 seconds.",
                "Show real‑time P&L updates for my open positions.",
                "What's the scanner progress after 30 seconds?",
                "What's the weather?"  # Off-topic test
            ],
            "Category E - Advanced Features": [
                "Send me the 8:00 AM CT market briefing.",
                "Retrieve Chapter 3 summary from the TTM Squeeze book.",
                "Place a paper trade: buy 100 AAPL at market.",
                "Report current CPU, memory, and API‐latency metrics.",
                "Check connectivity to all six specialized databases."
            ]
        }
    
    def test_chat_endpoint(self, message: str, context: str = "general") -> Dict[str, Any]:
        """Test a single chat message"""
        try:
            start_time = time.time()
            response = self.session.post(
                f"{self.base_url}/api/v1/chat/message",
                json={"message": message, "context": context}
            )
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                return {
                    "status": "SUCCESS",
                    "response": data.get("response", ""),
                    "response_time": response_time,
                    "status_code": response.status_code
                }
            else:
                return {
                    "status": "FAIL",
                    "response": f"HTTP {response.status_code}",
                    "response_time": response_time,
                    "status_code": response.status_code
                }
        except Exception as e:
            return {
                "status": "ERROR",
                "response": str(e),
                "response_time": 0,
                "status_code": None
            }
    
    def run_category_tests(self, category: str) -> List[Dict[str, Any]]:
        """Run all tests for a specific category"""
        print(f"\n🧪 Testing {category}")
        print("=" * 60)
        
        results = []
        questions = self.test_questions.get(category, [])
        
        for i, question in enumerate(questions, 1):
            print(f"\n{i}. {question}")
            print("-" * 40)
            
            result = self.test_chat_endpoint(question)
            results.append({
                "category": category,
                "question": question,
                "result": result
            })
            
            # Print result
            if result["status"] == "SUCCESS":
                response_text = result["response"][:200] + "..." if len(result["response"]) > 200 else result["response"]
                print(f"✅ SUCCESS ({result['response_time']:.2f}s)")
                print(f"Response: {response_text}")
            else:
                print(f"❌ {result['status']}: {result['response']}")
            
            # Small delay between requests
            time.sleep(0.5)
        
        return results
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all manual tests"""
        print("🚀 A.T.L.A.S. V4 Enhanced - Manual Test Runner")
        print("=" * 60)
        print(f"Testing against: {self.base_url}")
        print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        all_results = []
        
        for category in self.test_questions.keys():
            category_results = self.run_category_tests(category)
            all_results.extend(category_results)
        
        # Generate summary
        total_tests = len(all_results)
        successful = len([r for r in all_results if r["result"]["status"] == "SUCCESS"])
        failed = len([r for r in all_results if r["result"]["status"] == "FAIL"])
        errors = len([r for r in all_results if r["result"]["status"] == "ERROR"])
        
        summary = {
            "timestamp": datetime.now().isoformat(),
            "total_tests": total_tests,
            "successful": successful,
            "failed": failed,
            "errors": errors,
            "success_rate": (successful / total_tests * 100) if total_tests > 0 else 0,
            "results": all_results
        }
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 MANUAL TEST SUMMARY")
        print("=" * 60)
        print(f"Total Tests: {total_tests}")
        print(f"Successful: {successful} ✅")
        print(f"Failed: {failed} ❌")
        print(f"Errors: {errors} 🚨")
        print(f"Success Rate: {summary['success_rate']:.1f}%")
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"atlas_manual_test_results_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(summary, f, indent=2)
        
        print(f"\n📁 Results saved to: {filename}")
        
        return summary
    
    def test_specific_category(self, category: str):
        """Test a specific category only"""
        if category not in self.test_questions:
            print(f"❌ Category '{category}' not found.")
            print(f"Available categories: {list(self.test_questions.keys())}")
            return
        
        results = self.run_category_tests(category)
        
        # Quick summary for single category
        successful = len([r for r in results if r["result"]["status"] == "SUCCESS"])
        total = len(results)
        
        print(f"\n📊 Category Summary: {successful}/{total} tests passed ({successful/total*100:.1f}%)")
        
        return results


def main():
    """Main execution"""
    import sys
    
    runner = AtlasManualTestRunner()
    
    if len(sys.argv) > 1:
        # Test specific category
        category = sys.argv[1]
        runner.test_specific_category(category)
    else:
        # Run all tests
        runner.run_all_tests()


if __name__ == "__main__":
    main()
