"""
A.T.L.A.S. Enhanced Grok Trading Strategies - Integrated Version
Combines advanced Grok capabilities with our comprehensive trading plan engine
to deliver specific, actionable trading recommendations with real-time market intelligence.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import json
import numpy as np
import re

# Core A.T.L.A.S. imports
from config import get_api_config
from models import EngineStatus, SignalStrength, TradingSignal

# Enhanced Grok integration imports
try:
    from atlas_grok_integration import (
        AtlasGrokIntegrationEngine, GrokRequest, GrokResponse,
        GrokTaskType, GrokCapability
    )
    GROK_AVAILABLE = True
except ImportError:
    GROK_AVAILABLE = False

logger = logging.getLogger(__name__)

class EnhancedGrokTradingStrategy:
    """Enhanced Grok trading strategy integrated with our trading plan engine"""
    
    def __init__(self, name: str, target_return: float = 0.35):
        self.name = name
        self.target_return = target_return
        self.grok_engine = None
        self.performance_metrics = {
            'total_trades': 0,
            'winning_trades': 0,
            'total_return': 0.0,
            'max_drawdown': 0.0,
            'sharpe_ratio': 0.0,
            'grok_enhanced_trades': 0
        }
        
    async def initialize(self):
        """Initialize Grok enhancement for strategy"""
        try:
            if not GROK_AVAILABLE:
                logger.warning(f"⚠️ {self.name} strategy running without Grok enhancement")
                return False
                
            self.grok_engine = AtlasGrokIntegrationEngine()
            success = await self.grok_engine.initialize()
            
            if success:
                logger.info(f"✅ {self.name} strategy enhanced with Grok AI")
                return True
            else:
                logger.warning(f"⚠️ {self.name} strategy running without Grok enhancement")
                return False
                
        except Exception as e:
            logger.error(f"Failed to initialize Grok for {self.name}: {e}")
            return False

class MultiModalMarketIntelligenceStrategy(EnhancedGrokTradingStrategy):
    """Advanced strategy combining live search, reasoning, and structured outputs for trading plans"""
    
    def __init__(self):
        super().__init__("Multi-Modal Market Intelligence", target_return=0.40)
        self.intelligence_cache = {}
        self.market_regime_detector = None
        
    async def generate_specific_trading_opportunities(self, 
                                                    target_profit: float,
                                                    timeframe_days: int,
                                                    starting_capital: float,
                                                    symbols: List[str] = None) -> List[Dict[str, Any]]:
        """Generate specific trading opportunities using multi-modal intelligence"""
        if not self.grok_engine:
            return await self._fallback_opportunity_generation(target_profit, symbols or [])
        
        opportunities = []
        
        # Use top market symbols if none provided
        if not symbols:
            symbols = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'META', 'NFLX']
        
        for symbol in symbols[:8]:  # Limit to top 8 for performance
            try:
                # Step 1: Multi-modal market intelligence gathering
                intelligence = await self._gather_market_intelligence(symbol)
                
                # Step 2: Advanced reasoning analysis for specific trades
                trade_analysis = await self._perform_trade_reasoning_analysis(
                    symbol, intelligence, target_profit, timeframe_days, starting_capital
                )
                
                # Step 3: Generate structured trading opportunity
                structured_opportunity = await self._generate_structured_opportunity(
                    symbol, intelligence, trade_analysis, target_profit
                )
                
                if structured_opportunity and structured_opportunity.get('confidence', 0) > 0.70:
                    opportunities.append(structured_opportunity)
                    
            except Exception as e:
                logger.error(f"Opportunity generation failed for {symbol}: {e}")
                continue
        
        # Sort by confidence and return top opportunities
        opportunities.sort(key=lambda x: x.get('confidence', 0), reverse=True)
        return opportunities[:5]  # Return top 5 opportunities
    
    async def _gather_market_intelligence(self, symbol: str) -> Dict[str, Any]:
        """Gather comprehensive market intelligence using Grok live search"""
        cache_key = f"{symbol}_{datetime.now().strftime('%Y%m%d_%H')}"
        
        if cache_key in self.intelligence_cache:
            return self.intelligence_cache[cache_key]
        
        intelligence = {
            'symbol': symbol,
            'timestamp': datetime.now().isoformat(),
            'news_sentiment': {},
            'social_sentiment': {},
            'market_context': {},
            'technical_context': {},
            'earnings_context': {}
        }
        
        try:
            # Real-time news analysis with trading focus
            news_prompt = f"""
            Search for the latest breaking news, earnings updates, analyst upgrades/downgrades, 
            and market-moving events for {symbol}. Focus on information that would impact 
            short-term trading decisions and price movements in the next 1-30 days.
            
            Provide specific details about:
            - Recent earnings results or upcoming earnings dates
            - Analyst price target changes
            - Major product announcements or business developments
            - Sector trends affecting the stock
            - Any regulatory or legal developments
            """
            
            if self.grok_engine and hasattr(self.grok_engine, 'grok_client'):
                news_result = await self.grok_engine.grok_client.make_request(
                    GrokRequest(
                        task_type=GrokTaskType.LIVE_SEARCH,
                        capability=GrokCapability.SEARCH,
                        prompt=news_prompt,
                        temperature=0.2
                    )
                )
                
                if news_result.success:
                    intelligence['news_sentiment'] = {
                        'content': news_result.content,
                        'confidence': news_result.confidence,
                        'impact_score': self._calculate_news_impact(news_result.content),
                        'trading_catalyst': self._extract_trading_catalyst(news_result.content)
                    }
            
            # Social media and trader sentiment analysis
            social_prompt = f"""
            Analyze current social media sentiment and trader discussions for {symbol}.
            Look for:
            - Overall bullish/bearish sentiment
            - Unusual options activity mentions
            - Institutional buying/selling discussions
            - Technical analysis discussions
            - Momentum and trend discussions
            
            Provide a sentiment score and key themes.
            """
            
            if self.grok_engine and hasattr(self.grok_engine, 'grok_client'):
                social_result = await self.grok_engine.grok_client.make_request(
                    GrokRequest(
                        task_type=GrokTaskType.LIVE_SEARCH,
                        capability=GrokCapability.SEARCH,
                        prompt=social_prompt,
                        temperature=0.3
                    )
                )
                
                if social_result.success:
                    intelligence['social_sentiment'] = {
                        'content': social_result.content,
                        'confidence': social_result.confidence,
                        'sentiment_score': self._extract_sentiment_score(social_result.content),
                        'key_themes': self._extract_key_themes(social_result.content)
                    }
            
            # Cache the intelligence
            self.intelligence_cache[cache_key] = intelligence
            
        except Exception as e:
            logger.error(f"Error gathering market intelligence for {symbol}: {e}")
        
        return intelligence
    
    async def _perform_trade_reasoning_analysis(self, symbol: str, intelligence: Dict[str, Any],
                                              target_profit: float, timeframe_days: int,
                                              starting_capital: float) -> Dict[str, Any]:
        """Perform advanced reasoning analysis for specific trade setup"""
        if not self.grok_engine:
            return self._fallback_trade_analysis(symbol, target_profit)
        
        reasoning_prompt = f"""
        Based on the market intelligence for {symbol}, provide a detailed trading analysis for:
        
        TARGET: ${target_profit} profit in {timeframe_days} days
        CAPITAL: ${starting_capital}
        
        Intelligence Summary:
        - News Impact: {intelligence.get('news_sentiment', {}).get('impact_score', 'Unknown')}
        - Social Sentiment: {intelligence.get('social_sentiment', {}).get('sentiment_score', 'Unknown')}
        - Trading Catalyst: {intelligence.get('news_sentiment', {}).get('trading_catalyst', 'None identified')}
        
        Provide specific analysis including:
        1. Probability of achieving target profit in timeframe
        2. Recommended position size based on capital and risk
        3. Specific entry price range
        4. Target exit price
        5. Stop-loss level
        6. Key risks and catalysts
        7. Confidence score (0-100%)
        
        Be specific with numbers and actionable recommendations.
        """
        
        try:
            reasoning_result = await self.grok_engine.grok_client.make_request(
                GrokRequest(
                    task_type=GrokTaskType.TRADING_ANALYSIS,
                    capability=GrokCapability.REASONING,
                    prompt=reasoning_prompt,
                    temperature=0.2
                )
            )
            
            if reasoning_result.success:
                return {
                    'analysis': reasoning_result.content,
                    'confidence': reasoning_result.confidence,
                    'reasoning_quality': 'grok_enhanced'
                }
        except Exception as e:
            logger.error(f"Grok reasoning analysis failed for {symbol}: {e}")
        
        return self._fallback_trade_analysis(symbol, target_profit)

    async def _generate_structured_opportunity(self, symbol: str, intelligence: Dict[str, Any],
                                             trade_analysis: Dict[str, Any], target_profit: float) -> Dict[str, Any]:
        """Generate structured trading opportunity with specific parameters"""
        try:
            # Extract key information from analysis
            analysis_content = trade_analysis.get('analysis', '')

            # Parse specific trading parameters from Grok analysis
            entry_price = self._extract_price_from_analysis(analysis_content, 'entry')
            target_price = self._extract_price_from_analysis(analysis_content, 'target')
            stop_loss = self._extract_price_from_analysis(analysis_content, 'stop')
            confidence = self._extract_confidence_from_analysis(analysis_content)
            position_size = self._extract_position_size_from_analysis(analysis_content)

            # Get current market price for validation
            current_price = await self._get_current_price(symbol)

            # Validate and adjust parameters
            if not entry_price or entry_price <= 0:
                entry_price = current_price
            if not target_price or target_price <= entry_price:
                target_price = entry_price * 1.08  # 8% target
            if not stop_loss or stop_loss >= entry_price:
                stop_loss = entry_price * 0.95  # 5% stop loss

            # Calculate metrics
            profit_potential = (target_price - entry_price) * position_size
            risk_amount = (entry_price - stop_loss) * position_size
            risk_reward_ratio = profit_potential / risk_amount if risk_amount > 0 else 0

            # Extract trading catalyst and setup type
            catalyst = intelligence.get('news_sentiment', {}).get('trading_catalyst', 'Market momentum')
            setup_type = self._determine_setup_type(intelligence, trade_analysis)

            return {
                'symbol': symbol,
                'company_name': self._get_company_name(symbol),
                'current_price': current_price,
                'entry_price': entry_price,
                'target_price': target_price,
                'stop_loss': stop_loss,
                'position_size': position_size,
                'capital_allocation': entry_price * position_size,
                'confidence': confidence / 100.0 if confidence > 1 else confidence,
                'signal_strength': self._calculate_signal_strength(confidence),
                'opportunity_type': setup_type,
                'timeframe': 'swing',
                'catalyst': catalyst,
                'risk_reward_ratio': risk_reward_ratio,
                'expected_profit': profit_potential,
                'max_risk': risk_amount,
                'technical_setup': self._extract_technical_setup(analysis_content),
                'grok_enhanced': True,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error generating structured opportunity for {symbol}: {e}")
            return None

    def _extract_price_from_analysis(self, analysis: str, price_type: str) -> Optional[float]:
        """Extract specific price from Grok analysis"""
        try:
            patterns = {
                'entry': [r'entry.*?(\$?\d+\.?\d*)', r'buy.*?(\$?\d+\.?\d*)', r'enter.*?(\$?\d+\.?\d*)'],
                'target': [r'target.*?(\$?\d+\.?\d*)', r'sell.*?(\$?\d+\.?\d*)', r'exit.*?(\$?\d+\.?\d*)'],
                'stop': [r'stop.*?(\$?\d+\.?\d*)', r'loss.*?(\$?\d+\.?\d*)']
            }

            for pattern in patterns.get(price_type, []):
                match = re.search(pattern, analysis.lower())
                if match:
                    price_str = match.group(1).replace('$', '')
                    return float(price_str)

            return None
        except:
            return None

    def _extract_confidence_from_analysis(self, analysis: str) -> float:
        """Extract confidence score from analysis"""
        try:
            # Look for confidence patterns
            patterns = [
                r'confidence.*?(\d+)%',
                r'(\d+)%.*?confidence',
                r'probability.*?(\d+)%',
                r'(\d+)%.*?probability'
            ]

            for pattern in patterns:
                match = re.search(pattern, analysis.lower())
                if match:
                    return float(match.group(1))

            # Default confidence based on analysis quality
            return 75.0
        except:
            return 75.0

    def _extract_position_size_from_analysis(self, analysis: str) -> int:
        """Extract recommended position size from analysis"""
        try:
            # Look for share quantity patterns
            patterns = [
                r'(\d+)\s*shares',
                r'position.*?(\d+)',
                r'buy.*?(\d+)'
            ]

            for pattern in patterns:
                match = re.search(pattern, analysis.lower())
                if match:
                    return int(match.group(1))

            # Default position size
            return 100
        except:
            return 100

    async def _get_current_price(self, symbol: str) -> float:
        """Get current market price for symbol"""
        try:
            # This would integrate with your market data provider
            # For now, return a placeholder that varies by symbol
            base_prices = {
                'AAPL': 175.0, 'MSFT': 350.0, 'GOOGL': 140.0, 'AMZN': 145.0,
                'TSLA': 250.0, 'NVDA': 450.0, 'META': 320.0, 'NFLX': 400.0
            }
            return base_prices.get(symbol, 150.0)
        except:
            return 150.0

    def _get_company_name(self, symbol: str) -> str:
        """Get company name for symbol"""
        company_names = {
            'AAPL': 'Apple Inc.',
            'MSFT': 'Microsoft Corporation',
            'GOOGL': 'Alphabet Inc.',
            'AMZN': 'Amazon.com Inc.',
            'TSLA': 'Tesla Inc.',
            'NVDA': 'NVIDIA Corporation',
            'META': 'Meta Platforms Inc.',
            'NFLX': 'Netflix Inc.'
        }
        return company_names.get(symbol, f"{symbol} Corporation")

    def _calculate_signal_strength(self, confidence: float) -> int:
        """Calculate signal strength (1-5 stars) based on confidence"""
        if confidence >= 90:
            return 5
        elif confidence >= 80:
            return 4
        elif confidence >= 70:
            return 3
        elif confidence >= 60:
            return 2
        else:
            return 1

    def _determine_setup_type(self, intelligence: Dict[str, Any], trade_analysis: Dict[str, Any]) -> str:
        """Determine the type of trading setup"""
        analysis_content = trade_analysis.get('analysis', '').lower()

        if 'breakout' in analysis_content:
            return 'breakout'
        elif 'momentum' in analysis_content:
            return 'momentum'
        elif 'reversal' in analysis_content:
            return 'reversal'
        elif 'earnings' in analysis_content:
            return 'earnings_play'
        else:
            return 'technical_setup'

    def _extract_technical_setup(self, analysis: str) -> str:
        """Extract technical setup description from analysis"""
        # Look for technical indicators and patterns
        technical_terms = ['support', 'resistance', 'breakout', 'momentum', 'trend', 'pattern']

        sentences = analysis.split('.')
        for sentence in sentences:
            if any(term in sentence.lower() for term in technical_terms):
                return sentence.strip()

        return "Technical analysis indicates favorable setup"

    def _calculate_news_impact(self, news_content: str) -> float:
        """Calculate news impact score from content"""
        try:
            # Look for impact indicators
            high_impact_terms = ['earnings', 'upgrade', 'downgrade', 'acquisition', 'merger', 'breakthrough']
            medium_impact_terms = ['announcement', 'partnership', 'contract', 'guidance', 'revenue']

            content_lower = news_content.lower()

            high_count = sum(1 for term in high_impact_terms if term in content_lower)
            medium_count = sum(1 for term in medium_impact_terms if term in content_lower)

            impact_score = (high_count * 3 + medium_count * 1.5) / 10
            return min(impact_score, 10.0)  # Cap at 10
        except:
            return 5.0  # Default medium impact

    def _extract_trading_catalyst(self, news_content: str) -> str:
        """Extract main trading catalyst from news content"""
        try:
            # Look for key catalyst patterns
            catalyst_patterns = [
                r'earnings.*?(beat|miss|guidance)',
                r'analyst.*?(upgrade|downgrade)',
                r'(acquisition|merger|partnership)',
                r'(breakthrough|innovation|product launch)'
            ]

            for pattern in catalyst_patterns:
                match = re.search(pattern, news_content.lower())
                if match:
                    return match.group(0).title()

            return "Market momentum and technical setup"
        except:
            return "Market momentum and technical setup"

    def _extract_sentiment_score(self, social_content: str) -> float:
        """Extract sentiment score from social media content"""
        try:
            # Simple sentiment analysis based on keywords
            bullish_terms = ['bullish', 'buy', 'long', 'moon', 'rocket', 'strong', 'positive']
            bearish_terms = ['bearish', 'sell', 'short', 'crash', 'weak', 'negative', 'dump']

            content_lower = social_content.lower()

            bullish_count = sum(1 for term in bullish_terms if term in content_lower)
            bearish_count = sum(1 for term in bearish_terms if term in content_lower)

            if bullish_count + bearish_count == 0:
                return 0.5  # Neutral

            sentiment = bullish_count / (bullish_count + bearish_count)
            return sentiment
        except:
            return 0.5  # Default neutral

    def _extract_key_themes(self, social_content: str) -> List[str]:
        """Extract key themes from social media content"""
        try:
            themes = []
            theme_patterns = [
                r'(technical analysis|TA)',
                r'(options flow|unusual activity)',
                r'(institutional|smart money)',
                r'(momentum|trend)',
                r'(earnings|guidance)',
                r'(breakout|support|resistance)'
            ]

            for pattern in theme_patterns:
                if re.search(pattern, social_content.lower()):
                    themes.append(pattern.split('|')[0].replace('(', '').title())

            return themes[:3]  # Return top 3 themes
        except:
            return ['Technical Analysis', 'Market Momentum']

    async def _fallback_opportunity_generation(self, target_profit: float, symbols: List[str]) -> List[Dict[str, Any]]:
        """Fallback opportunity generation when Grok is not available"""
        opportunities = []

        for symbol in symbols[:3]:  # Limit to 3 for fallback
            try:
                current_price = await self._get_current_price(symbol)

                opportunity = {
                    'symbol': symbol,
                    'company_name': self._get_company_name(symbol),
                    'current_price': current_price,
                    'entry_price': current_price,
                    'target_price': current_price * 1.06,  # 6% target
                    'stop_loss': current_price * 0.96,     # 4% stop
                    'position_size': 100,
                    'capital_allocation': current_price * 100,
                    'confidence': 0.65,
                    'signal_strength': 3,
                    'opportunity_type': 'technical_setup',
                    'timeframe': 'swing',
                    'catalyst': 'Technical momentum pattern',
                    'risk_reward_ratio': 1.5,
                    'expected_profit': current_price * 6,
                    'max_risk': current_price * 4,
                    'technical_setup': 'Basic technical analysis setup',
                    'grok_enhanced': False,
                    'timestamp': datetime.now().isoformat()
                }

                opportunities.append(opportunity)

            except Exception as e:
                logger.error(f"Fallback opportunity generation failed for {symbol}: {e}")
                continue

        return opportunities

    def _fallback_trade_analysis(self, symbol: str, target_profit: float) -> Dict[str, Any]:
        """Fallback trade analysis when Grok is not available"""
        return {
            'analysis': f"Basic technical analysis for {symbol} targeting ${target_profit} profit. "
                       f"Entry at current market price with 6% upside target and 4% stop loss. "
                       f"Moderate confidence based on technical indicators.",
            'confidence': 0.65,
            'reasoning_quality': 'fallback'
        }

# Compatibility classes removed - using main atlas_grok_integration imports

# Global instance for easy access
enhanced_grok_strategy = MultiModalMarketIntelligenceStrategy()
