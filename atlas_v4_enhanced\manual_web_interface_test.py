#!/usr/bin/env python3
"""
Manual Web Interface Test for Atlas V5 Enhanced
Tests specific README.md examples through the web interface
"""

import asyncio
import aiohttp
import json
from datetime import datetime

async def test_readme_examples():
    """Test specific examples from README.md"""
    
    base_url = "http://localhost:8002"
    
    # README.md example queries to test
    test_queries = [
        {
            "name": "README Example 1",
            "query": "Make me $100 today",
            "expected": "Trading plan with specific dollar target"
        },
        {
            "name": "README Example 2", 
            "query": "Scan the S&P 500 for stocks ready to pop",
            "expected": "Market scanning with pattern detection"
        },
        {
            "name": "README Example 3",
            "query": "What is RSI and how does it work?",
            "expected": "Educational content about technical indicators"
        },
        {
            "name": "README Example 4",
            "query": "Analyze AAPL for a potential trade",
            "expected": "6-point trading analysis format"
        },
        {
            "name": "6-Point Format Test",
            "query": "Should I buy NVDA now?",
            "expected": "6-point format with confidence scoring"
        },
        {
            "name": "Multi-Symbol Test",
            "query": "Compare AAPL, MSFT, and GOOGL for best trading opportunity",
            "expected": "Comparative analysis of multiple stocks"
        },
        {
            "name": "Options Strategy Test",
            "query": "What about TSLA options instead of shares?",
            "expected": "Options analysis with strategy recommendations"
        },
        {
            "name": "Lee Method Test",
            "query": "Find TTM Squeeze patterns in tech stocks",
            "expected": "Lee Method pattern detection"
        },
        {
            "name": "Beginner Education Test",
            "query": "I'm new to trading, where should I start?",
            "expected": "Beginner-friendly guidance with risk warnings"
        },
        {
            "name": "Real-time News Test",
            "query": "What's the latest news on Tesla affecting stock price?",
            "expected": "Grok-powered news analysis"
        }
    ]
    
    print("="*80)
    print("ATLAS V5 ENHANCED - MANUAL WEB INTERFACE TEST")
    print("Testing README.md Examples")
    print("="*80)
    
    async with aiohttp.ClientSession() as session:
        for i, test in enumerate(test_queries, 1):
            print(f"\n[{i}/10] Testing: {test['name']}")
            print(f"Query: '{test['query']}'")
            print(f"Expected: {test['expected']}")
            print("-" * 60)
            
            try:
                start_time = datetime.now()
                
                async with session.post(
                    f"{base_url}/api/v1/chat",
                    json={"message": test['query']},
                    timeout=aiohttp.ClientTimeout(total=45)
                ) as response:
                    
                    end_time = datetime.now()
                    response_time = (end_time - start_time).total_seconds()
                    
                    if response.status == 200:
                        result = await response.json()
                        
                        print(f"✅ Status: SUCCESS")
                        print(f"⏱️  Response Time: {response_time:.2f}s")
                        print(f"🤖 Grok Powered: {result.get('grok_powered', 'Unknown')}")
                        print(f"🎯 Intent Type: {result.get('intent_type', 'Unknown')}")
                        print(f"📊 Confidence: {result.get('confidence', 'Unknown')}")
                        print(f"🧠 Context Aware: {result.get('context_aware', 'Unknown')}")
                        
                        # Show first 200 characters of response
                        response_text = result.get('response', '')
                        if len(response_text) > 200:
                            preview = response_text[:200] + "..."
                        else:
                            preview = response_text
                        
                        print(f"📝 Response Preview: {preview}")
                        
                        # Check for 6-point format
                        six_point_indicators = ["1️⃣", "2️⃣", "3️⃣", "4️⃣", "5️⃣", "6️⃣"]
                        six_point_count = sum(1 for indicator in six_point_indicators if indicator in response_text)
                        
                        if six_point_count >= 4:
                            print("🎯 6-Point Format: ✅ DETECTED")
                        elif six_point_count > 0:
                            print(f"🎯 6-Point Format: ⚠️ PARTIAL ({six_point_count}/6)")
                        else:
                            print("🎯 6-Point Format: ❌ NOT DETECTED")
                        
                    else:
                        print(f"❌ Status: FAILED (HTTP {response.status})")
                        
            except Exception as e:
                print(f"❌ Status: ERROR - {str(e)}")
            
            print("-" * 60)
            
            # Wait between requests
            if i < len(test_queries):
                print("⏳ Waiting 3 seconds before next test...")
                await asyncio.sleep(3)
    
    print("\n" + "="*80)
    print("MANUAL TEST COMPLETED")
    print("="*80)
    print("\n🌐 Web Interface: http://localhost:8002")
    print("📊 Test Results: See output above")
    print("📋 Next Steps: Review web interface visually for:")
    print("   • Intent detection bubbles")
    print("   • Visual formatting")
    print("   • Mobile responsiveness")
    print("   • WebSocket real-time updates")
    print("="*80)

if __name__ == "__main__":
    asyncio.run(test_readme_examples())
