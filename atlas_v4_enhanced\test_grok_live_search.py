#!/usr/bin/env python3
"""
Test script to verify Grok live search integration with FMP/Alpaca data
"""

import asyncio
import json
import httpx
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_enhanced_capabilities():
    """Test enhanced Grok live search capabilities"""
    
    base_url = "http://localhost:8002"
    
    test_queries = [
        {
            "name": "Stock Analysis with Live Search",
            "message": "What is AAPL trading at and what's the latest news?",
            "expected_features": ["fmp_data", "live_search", "enhanced"]
        },
        {
            "name": "Market Sentiment Query",
            "message": "What's the current market sentiment for TSLA?",
            "expected_features": ["sentiment", "live_search", "social"]
        },
        {
            "name": "Breaking News Query",
            "message": "Any breaking news affecting the tech sector?",
            "expected_features": ["news", "live_search", "sector_analysis"]
        },
        {
            "name": "General Market Intelligence",
            "message": "What are the current market trends and outlook?",
            "expected_features": ["market_analysis", "live_search", "trends"]
        }
    ]
    
    async with httpx.AsyncClient(timeout=60.0) as client:
        for test in test_queries:
            try:
                logger.info(f"\n🧪 Testing: {test['name']}")
                logger.info(f"📝 Query: '{test['message']}'")
                
                # Send enhanced query
                response = await client.post(
                    f"{base_url}/api/v1/chat/message",
                    json={
                        "message": test["message"],
                        "context": "general",
                        "session_id": f"test_enhanced_{test['name'].lower().replace(' ', '_')}"
                    }
                )
                
                if response.status_code == 200:
                    data = response.json()
                    logger.info(f"✅ SUCCESS: {test['name']}")
                    
                    # Check for enhanced features
                    response_text = data.get('response', '')
                    metadata = data.get('metadata', {})
                    
                    # Look for live search indicators
                    has_live_search = (
                        "Live Market Intelligence" in response_text or
                        "powered by Grok" in response_text or
                        metadata.get('live_search_enabled', False) or
                        "enhanced_with_live_search" in str(metadata)
                    )
                    
                    # Look for data source attribution
                    has_multi_source = (
                        "FMP" in response_text or
                        "Alpaca" in response_text or
                        "Data Sources" in response_text
                    )
                    
                    logger.info(f"🌐 Live Search Detected: {has_live_search}")
                    logger.info(f"📊 Multi-Source Data: {has_multi_source}")
                    logger.info(f"🎯 Intent: {data.get('intent_type', 'unknown')}")
                    logger.info(f"📊 Confidence: {data.get('confidence', 0):.2f}")
                    
                    if metadata.get('data_sources'):
                        logger.info(f"🔗 Data Sources: {metadata['data_sources']}")
                    
                    # Preview response
                    preview = response_text[:200] + "..." if len(response_text) > 200 else response_text
                    logger.info(f"📤 Response Preview: {preview}")
                    
                else:
                    logger.error(f"❌ HTTP ERROR: {test['name']} - Status: {response.status_code}")
                    logger.error(f"Response: {response.text}")
                    
            except Exception as e:
                logger.error(f"❌ EXCEPTION: {test['name']} - {e}")
                
            # Wait between tests
            await asyncio.sleep(2)

if __name__ == "__main__":
    asyncio.run(test_enhanced_capabilities())
